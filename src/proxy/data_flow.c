#include "proxy/data_flow.h"
#include "common/hex_utils.h"
#include "common/protocol.h"
#include "log.h"
#include <errno.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// Forward declarations
static int handle_sitp_to_tcp_message(proxy_data_flow_t *flow, message_t *msg);
static int process_sitp_data_command(proxy_data_flow_t *flow, message_t *msg);
static int process_sitp_disconnect_command(proxy_data_flow_t *flow,
                                           message_t *msg);
static int process_sitp_ping_command(proxy_data_flow_t *flow, message_t *msg);
static ssize_t read_pipe_data(int fd, uint8_t *buffer, size_t buffer_size);
static void send_disconnect_notification(proxy_data_flow_t *flow,
                                         uint32_t connection_id);

static proxy_data_flow_t *global_data_flow = NULL;

int proxy_data_flow_init(proxy_data_flow_t *flow,
                         tcp_client_manager_t *tcp_manager,
                         proxy_sitp_client_t *sitp_client, proxy_pipes_t *pipes,
                         proxy_config_t *config) {
  flow->tcp_manager = tcp_manager;
  flow->sitp_client = sitp_client;
  flow->pipes = pipes;
  flow->config = config;

  global_data_flow = flow;

  log_info("Proxy data flow initialized");
  return 0;
}

void proxy_data_flow_cleanup(proxy_data_flow_t *flow) {
  global_data_flow = NULL;
}

// Pipe callback implementations
void sitp_to_tcp_pipe_callback(int fd, short event, void *arg) {
  if (!global_data_flow) {
    log_error("Data flow not initialized");
    return;
  }

  uint8_t buffer[8192];
  message_t *msg = NULL;

  ssize_t bytes_read = read_pipe_data(fd, buffer, sizeof(buffer));
  if (bytes_read <= 0) {
    return;
  }

  log_debug("Processing %zd bytes from SITP pipe", bytes_read);

  if (!validate_padded_message(buffer, bytes_read,
                               global_data_flow->config->padding_size)) {
    log_warn("Invalid padded message received from SITP pipe");
    return;
  }

  msg = extract_message_from_padded(buffer, bytes_read,
                                    global_data_flow->config->padding_size);
  if (!msg) {
    log_error("Failed to extract message from padded buffer");
    return;
  }

  if (handle_sitp_to_tcp_message(global_data_flow, msg) < 0) {
    log_error("Failed to handle SITP to TCP message");
  }

  free(msg);
}

void tcp_to_sitp_pipe_callback(int fd, short event, void *arg) {
  if (!global_data_flow) {
    log_error("Data flow not initialized");
    return;
  }

  uint8_t buffer[8192];
  ssize_t bytes_read = read_pipe_data(fd, buffer, sizeof(buffer));
  if (bytes_read <= 0) {
    return;
  }

  // Forward TCP data back to SITP (this is for responses from target server)
  if (proxy_sitp_client_send(global_data_flow->sitp_client, buffer,
                             bytes_read) < 0) {
    log_error("Failed to send data to SITP");
  }
}

static ssize_t read_pipe_data(int fd, uint8_t *buffer, size_t buffer_size) {
  ssize_t bytes_read = read(fd, buffer, buffer_size);

  if (bytes_read < 0) {
    if (errno != EAGAIN && errno != EWOULDBLOCK) {
      log_error("Error reading from pipe: fd=%d, error=%s", fd,
                strerror(errno));
    }
  } else if (bytes_read == 0) {
    log_debug("Pipe closed: fd=%d", fd);
  }

  return bytes_read;
}

// Handle SITP to TCP message routing
static int handle_sitp_to_tcp_message(proxy_data_flow_t *flow, message_t *msg) {
  if (!flow || !msg) {
    log_error("Invalid parameters for SITP to TCP message handling");
    return -1;
  }

  if (flow->config->verbose) {
    log_debug("Processing SITP message: client_fd=%u, cmd=%d, data_len=%u",
              msg->client_fd, msg->cmd, msg->data_len);
    if (msg->data_len > 0) {
      print_hexdump(msg->data, msg->data_len);
    }
  }

  switch (msg->cmd) {
  case CMD_DATA:
    return process_sitp_data_command(flow, msg);
  case CMD_DISCONNECT:
    return process_sitp_disconnect_command(flow, msg);
  case CMD_PING:
    return process_sitp_ping_command(flow, msg);
  default:
    log_warn("Unknown command received from SITP: %d", msg->cmd);
    return -1;
  }
}

// Process SITP data command
static int process_sitp_data_command(proxy_data_flow_t *flow, message_t *msg) {
  log_debug("Forwarding %u bytes from SITP to TCP server for client %u",
            msg->data_len, msg->client_fd);

  // Check if TCP connection exists for this client_fd
  tcp_connection_t *conn =
      tcp_client_get_connection(flow->tcp_manager, msg->client_fd);
  if (!conn) {
    // Connection doesn't exist, create new one and send data
    log_info(
        "Received data for non-existent connection %u, creating new connection",
        msg->client_fd);
    conn = tcp_client_create_connection(flow->tcp_manager, msg->client_fd);
    if (!conn) {
      log_error("Failed to create TCP connection for client %u",
                msg->client_fd);
      // Send disconnect notification back to bridge since connection creation
      // failed
      send_disconnect_notification(flow, msg->client_fd);
      return -1;
    }
  }

  // Send data to TCP server (will be buffered if connection not yet
  // established)
  if (tcp_client_send_data(conn, msg->data, msg->data_len) < 0) {
    log_error("Failed to send data to TCP server for client %u",
              msg->client_fd);
    return -1;
  }

  return 0;
}

// Process SITP disconnect command
static int process_sitp_disconnect_command(proxy_data_flow_t *flow,
                                           message_t *msg) {
  log_info("Received disconnect notification from SITP for client %u",
           msg->client_fd);

  // Close TCP connection
  tcp_client_close_connection(flow->tcp_manager, msg->client_fd);
  log_info("Closed TCP connection for client %u", msg->client_fd);

  return 0;
}

// Process SITP ping command
static int process_sitp_ping_command(proxy_data_flow_t *flow, message_t *msg) {
  log_debug("Received ping from SITP for client %u", msg->client_fd);

  // Respond with PONG
  padded_message_t *pong = create_padded_message(msg->client_fd, CMD_PONG, NULL,
                                                 0, flow->config->padding_size);
  if (!pong) {
    log_error("Failed to create PONG message for client %u", msg->client_fd);
    return -1;
  }

  if (proxy_pipes_write_tcp_to_sitp(flow->pipes, pong->buffer,
                                    pong->total_size) < 0) {
    log_error("Failed to send PONG response for client %u", msg->client_fd);
    free_padded_message(pong);
    return -1;
  }

  free_padded_message(pong);
  return 0;
}

static void send_disconnect_notification(proxy_data_flow_t *flow,
                                         uint32_t connection_id) {
  padded_message_t *disconnect_msg = create_padded_message(
      connection_id, CMD_DISCONNECT, NULL, 0, flow->config->padding_size);
  if (disconnect_msg) {
    if (proxy_pipes_write_tcp_to_sitp(flow->pipes, disconnect_msg->buffer,
                                      disconnect_msg->total_size) < 0) {
      log_error("Failed to send disconnect notification for connection %u",
                connection_id);
    }
    free_padded_message(disconnect_msg);
  }
}

// Function to be called from tcp_client.c when TCP connection receives data
void proxy_tcp_data_received(uint32_t connection_id, const uint8_t *data,
                             size_t len) {
  if (!global_data_flow) {
    return;
  }

  // Create protocol message with TCP data
  padded_message_t *msg =
      create_padded_message(connection_id, CMD_DATA, data, len,
                            global_data_flow->config->padding_size);
  if (msg) {
    if (proxy_pipes_write_tcp_to_sitp(global_data_flow->pipes, msg->buffer,
                                      msg->total_size) < 0) {
      log_error("Failed to forward TCP data to SITP for connection %u",
                connection_id);
    }
    free_padded_message(msg);
  }
}

// Function to be called from tcp_client.c when TCP connection is closed
void proxy_tcp_connection_closed(uint32_t connection_id) {
  if (!global_data_flow) {
    return;
  }

  send_disconnect_notification(global_data_flow, connection_id);
}
